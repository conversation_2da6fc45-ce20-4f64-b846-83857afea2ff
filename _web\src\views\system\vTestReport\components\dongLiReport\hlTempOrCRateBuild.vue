<template>
  <div class="hl-temp-wrapper">
    <div class="left-content block">
      <div class="flex-sb-center-row">
        <h3>一、测试数据选择</h3>
        <div style="float: right">
          <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            this.hlTempOrCRateParam.orderDataList = []
            this.deleteSelectedRowKeys = []
            this.$emit('handleVerify', this._handleVerify())
            }">
            <a-button class="mr5">清空</a-button>
          </a-popconfirm>
          <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectOrderData">
            <a-button>删除</a-button>
          </a-popconfirm>
        </div>
      </div>
      <div ref="tableContainer">
        <a-table class="mt10"
                 bordered
                 id="orderDataTable"
                 :columns="orderColumns"
                 :data-source="hlTempOrCRateParam.orderDataList"
                 :row-selection="deleteRowSelection"
                 childrenColumnName="child"
                 :rowKey="record => record.uuid"
                 :pagination="false"
        >
          <template slot="action" slot-scope="text, record, index, columns">
            <a-tooltip placement="top" title="删除" arrow-point-at-center>
              <a @click="deleteOrderDataOne(record, index)" style="text-align: center">
                <a-icon type="delete" style="font-size: large; margin-right: 3px;"/>
              </a>
            </a-tooltip>
            <a-tooltip placement="top" title="拖拽修改位置" arrow-point-at-center>
              <a-icon style="color: #1890ff; font-size: large; cursor: move;" heignt="18" width="18" class="drag" type="unordered-list"/>
            </a-tooltip>
          </template>
          <template slot="celltestcode" slot-scope="text, record, index, columns">
            <a @click="$refs.orderDataSelectModal.openStepData(record)" v-if="record.flowId != null" style="text-align: center">{{ text }}</a>
            <span v-else style="text-align: center">{{ text }}</span>
          </template>
          <template slot="dataPath" slot-scope="text, record, index, columns">
            <a-tooltip placement="topLeft" :overlayStyle="{ maxWidth: '600px' }">
              <template slot="title">
                {{ text ? text : record.testcontent }}
              </template>
              {{ text ? text : record.testcontent }}
            </a-tooltip>
          </template>
          <template slot="footer">
            <div class="footer-btn" :class="{ 'plus-btn': Array.isArray(hlTempOrCRateParam.orderDataList) && hlTempOrCRateParam.orderDataList.length === 0 }" @click="$refs.orderDataSelectModal.visible = true">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
              <a-icon type="plus"></a-icon>
            </div>
          </template>
        </a-table>
      </div>
    </div>

    <div class="right-content ml10">
      <div class="block">
        <h3>二、基本信息填写</h3>
        <pbiSearchContainer>
          <pbiSearchItem :span="8" label='项目名称' >
            <a-tooltip :title="hlTempOrCRateParam.projectName">
              <a-input v-model="hlTempOrCRateParam.projectName" @blur="() => {this.$emit('handleVerify', this._handleVerify())}"/>
            </a-tooltip>
          </pbiSearchItem>
          <pbiSearchItem :span="8" label='样品阶段' >
            <a-input v-model="hlTempOrCRateParam.phase" @blur="() => {this.$emit('handleVerify', this._handleVerify())}"/>
          </pbiSearchItem>
          <pbiSearchItem :span="8" :label="reportType === 'HlTemp' ? '倍率' : '温度'" >
            <a-input :suffix="reportType === 'HlTemp' ? 'C' : '℃'"
                     v-model="hlTempOrCRateParam.tempOrCRate"
                     @keyup="hlTempOrCRateParam.tempOrCRate = (hlTempOrCRateParam.tempOrCRate + '').replace(/[^0-9.-/]/g, '')"/>
          </pbiSearchItem>
          <pbiSearchItem :span="8" label='图表电压上限' >
            <a-input suffix="V"
                     v-model="hlTempOrCRateParam.yaxisMax"
                     @keyup="hlTempOrCRateParam.yaxisMax = (hlTempOrCRateParam.yaxisMax + '').replace(/[^0-9.-]/g, '')"
                     @blur="handleNumberBlur(null, null, 'yaxisMax')"/>
          </pbiSearchItem>
          <pbiSearchItem :span="8" label='图表电压下限' >
            <a-input suffix="V"
                     v-model="hlTempOrCRateParam.yaxisMin"
                     @keyup="hlTempOrCRateParam.yaxisMin = (hlTempOrCRateParam.yaxisMin + '').replace(/[^0-9.-]/g, '')"
                     @blur="handleNumberBlur(null, null, 'yaxisMin')"/>
          </pbiSearchItem>
        </pbiSearchContainer>
      </div>
      <div class="block mt8">
        <h3>三、模型生成逻辑</h3>
        <div class="flex-sb-center-row mt8">
          <div style="align-items: center;">
            充放电类型：
            <a-radio-group v-model="hlTempOrCRateParam.chOrDchType" @change="handleChOrDchTypeChange">
              <a-radio value="Charge">充电</a-radio>
              <a-radio value="Discharge">放电</a-radio>
            </a-radio-group>
          </div>
          <div style="float: right">
            <a-popconfirm placement="topRight" title="确认清空？" @confirm="() => {
            this.hlTempOrCRateParam.stepParamList = []
            this.stepParamSelectedRowKeys = []
            }">
              <a-button class="mr5">清空</a-button>
            </a-popconfirm>
            <a-popconfirm placement="topRight" title="确认删除？" @confirm="deleteSelectStepParam">
              <a-button>删除</a-button>
            </a-popconfirm>
          </div>
        </div>
        <div ref="paramTableContainer">
          <a-table class="step-param-table mt10"
                   :columns="reportType === 'HlTemp' ? stepParamColumns : cRateColumns"
                   :data-source="hlTempOrCRateParam.stepParamList"
                   :row-selection="stepParamRowSelection"
                   :rowKey="record => record._uid"
                   :pagination="false"
                   bordered>
            <template slot="action" slot-scope="text, record, index, columns">
              <a-tooltip placement="top" title="添加行" arrow-point-at-center>
                <a-icon type="plus" class="btn-icon" @click="addOne(index + 1)" />
              </a-tooltip>
              <a-tooltip placement="top" title="删除行" arrow-point-at-center>
                <a-icon type="delete" class="ml10 btn-icon" @click="deleteOne(index)" />
              </a-tooltip>
              <a-tooltip placement="top" title="拖拽修改位置" arrow-point-at-center>
                <a-icon style="color: #1890ff; font-size: large; cursor: move;" heignt="18" width="18" class="ml10 drag" type="unordered-list"/>
              </a-tooltip>
            </template>
            <template slot="tempOrCRateTitle">
              {{chOrDchTypeName + (reportType === 'HlTemp' ? '温度' : '倍率')}}
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center :title="`${reportType === 'HlTemp' ? '温度' : '倍率'}信息来源于测试内容-详情的${reportType === 'HlTemp' ? '温度' : '倍率'}`">
                <a-icon type="question-circle" style="color: #1890ff;"/>
              </a-tooltip>
            </template>
            <template slot="tempOrCRate" slot-scope="text, record, index, columns">
              <a-input :suffix="reportType === 'HlTemp' ? '℃' : 'C'"
                       v-model="record.tempOrCRate"
                       @keyup="record.tempOrCRate = (record.tempOrCRate+'').replaceAll(/[^0-9.-/]/g, '')"
                       @paste="copyFromExcel($event, 1, index)"/>
            </template>
            <template slot="ceStepTitle">
              {{ chOrDchTypeName }}工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center title="如是多个工步号，需以逗号,隔开，计算平均值">
                <a-icon v-if="reportType === 'HlTemp'" type="question-circle" style="color: #1890ff;"/>
              </a-tooltip>
            </template>
            <template slot="ceStep" slot-scope="text, record, index, columns">
              <a-input v-model="record.ceStep"
                       @keyup="verifyStepNumber(index, 'ceStep')"
                       @paste="copyFromExcel($event, 2, index)"/>
            </template>
            <template slot="recordStepTitle">
              详细数据工步号
              <a-tooltip placement="topRight" :overlayStyle="{ maxWidth: '600px' }" arrow-point-at-center :title="`详细数据工步号可不填，默认取最后一个${chOrDchTypeName}工步号`">
                <a-icon type="question-circle" style="color: #1890ff;"/>
              </a-tooltip>
            </template>
            <template slot="recordStep" slot-scope="text, record, index, columns">
              <a-input v-model="record.recordStep"
                       @keyup="verifyStepNumber(index, 'recordStep')"
                       @paste="copyFromExcel($event, 3, index)"/>
            </template>
            <template slot="standardIndex" slot-scope="text, record, index, columns">
              <a-radio :checked="index === hlTempOrCRateParam.standardIndex" @change="onRadioChange(index)"></a-radio>
            </template>
            <template slot="footer">
              <div class="footer-btn" :class="{ 'plus-btn': Array.isArray(hlTempOrCRateParam.orderDataList) && hlTempOrCRateParam.orderDataList.length > 0 && Array.isArray(hlTempOrCRateParam.stepParamList) && hlTempOrCRateParam.stepParamList.length === 0 }" @click="addOne(hlTempOrCRateParam.stepParamList.length)">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <a-icon type="plus"></a-icon>
              </div>
            </template>
          </a-table>
        </div>
      </div>
    </div>

    <orderDataSelectModal ref="orderDataSelectModal" :selectedOrderDataList="hlTempOrCRateParam.orderDataList"/>
  </div>
</template>

<script>
import orderDataSelectModal from "@/views/system/vTestReport/components/dongLiReport/orderDataSelectModal";
import {getTemGradientByOrdtaskId, getConditionsByOrdtaskId} from "@/api/modular/system/cycleReportManager";
import moment from "moment";
import Sortable from 'sortablejs';

export default {
  name: "hlTempOrCRateBuild",
  components: {
    orderDataSelectModal
  },
  props: {
    reportQueryParam: {
      type: Object,
    },
    hasQueryParamFlag: {
      type: Boolean,
      default: false,
    },
    reportType: {
      type: String,
    },
    width:{
      type: Number,
      default: 0
    },
    padding:{
      type: String,
      default: '8px'
    }

  },
  data() {
    return {
      editInitFlag: false,
      // 高低温或倍率查询参数
      hlTempOrCRateParam: {
        projectName: '',
        phase: '',
        tempOrCRate: '',
        yaxisMax: '4.4',
        yaxisMin: '2',
        orderDataList: [], // 选择的测试数据
        stepParamList: [], // 工步参数列表
        standardIndex: null, // 容量&能量保持率计算-基准工步索引
      },
      chOrDchTypeName: '',
      // 测试数据列表
      orderColumns: [
        {
          title: "操作",
          align: "center",
          width: 40,
          scopedSlots: {customRender: "action"}
        },
        {
          title: "序号",
          align: "center",
          width: 30,
          customRender: (text, record, index) => index + 1
        },
        {
          title: "委托单号",
          dataIndex: "folderno",
          align: "center",
          width: 45
        },
        {
          title: "测试项目别名",
          width: 65,
          align: "center",
          dataIndex: "alias"
        },
        {
          title: "样品编号",
          width: 50,
          align: "center",
          dataIndex: "orderno"
        },
        {
          title: "测试编码",
          width: 50,
          align: "center",
          dataIndex: "celltestcode",
          scopedSlots: {customRender: "celltestcode"}
        },
        {
          title: "数据位置",
          width: 50,
          align: "center",
          dataIndex: "dataPath",
          ellipsis: true,
          scopedSlots: {customRender: 'dataPath'},
        },
        /*{
          title: "设备编号",
          width: 45,
          align: "center",
          dataIndex: "equiptcode",
          customRender: (text, record, index) => {
            return (null != text ? text : "") + "-" + (null != record.channelno ? record.channelno : "")
          }
        },*/
        {
          title: "开始时间",
          width: 50,
          align: "center",
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
        {
          title: "结束时间",
          width: 50,
          align: "center",
          dataIndex: "endTime",
          customRender: (text, record, index) => {
            if (null != text) {
              return moment(text).format("YYYY-MM-DD")
            }
            return text
          }
        },
      ],
      deleteSelectedRowKeys: [],
      deleteRowSelection: {
        columnWidth: 20,
        onChange: (selectedRowKeys, selectedRows) => {
          this.deleteSelectedRowKeys = selectedRowKeys
        }
      },
      // 工步参数列表
      cRateColumns: [
        {
          title: "操作",
          width: 80,
          align: "center",
          scopedSlots: {customRender: "action"}
        },
        {
          width: 100,
          align: "center",
          dataIndex: "tempOrCRate",
          scopedSlots: {
            title: "tempOrCRateTitle",
            customRender: "tempOrCRate"
          }
        },
        {
          width: 130,
          align: "center",
          dataIndex: "ceStep",
          scopedSlots: {
            title: "ceStepTitle",
            customRender: "ceStep"
          }
        },
        {
          title: "容量&能量基准",
          width: 100,
          align: "center",
          scopedSlots: {customRender: "standardIndex"}
        },
      ],
      stepParamColumns: [
        {
          title: "操作",
          width: 80,
          align: "center",
          scopedSlots: {customRender: "action"}
        },
        {
          width: 100,
          align: "center",
          dataIndex: "tempOrCRate",
          scopedSlots: {
            title: "tempOrCRateTitle",
            customRender: "tempOrCRate"
          }
        },
        {
          width: 130,
          align: "center",
          dataIndex: "ceStep",
          scopedSlots: {
            title: "ceStepTitle",
            customRender: "ceStep"
          }
        },
        {
          // title: "详细数据工步号",
          width: 130,
          align: "center",
          dataIndex: "recordStep",
          scopedSlots: {
            title: "recordStepTitle",
            customRender: "recordStep"
          }
        },
        {
          title: "容量&能量基准",
          width: 100,
          align: "center",
          scopedSlots: {customRender: "standardIndex"}
        },
      ],
      stepParamSelectedRowKeys: [],
      stepParamRowSelection: {
        columnWidth: 20,
        onChange: (selectedRowKeys, selectedRows) => {
          this.stepParamSelectedRowKeys = selectedRowKeys
        }
      },
    }
  },
  computed: {
    ordtaskidToWatch() {
      // 创建一个计算属性，用于监听 第一个 ordtaskid
      const orderDataList = this.hlTempOrCRateParam.orderDataList;
      if (Array.isArray(orderDataList) && orderDataList.length > 0) {
        return orderDataList[0].ordtaskid;
      }
      return null; // 如果 ordtaskid 不存在，返回 null
    },
  },
  watch: {
    reportType(newVal, oldVal) {
      // 切换建模类型情况建模参数
      this.hlTempOrCRateParam = {
        projectName: '',
        phase: '',
        tempOrCRate: '',
        yaxisMax: '4.4',
        yaxisMin: '2',
        orderDataList: [], // 选择的测试数据
        stepParamList: [], // 工步参数列表
        standardIndex: null, // 容量&能量保持率计算-基准工步索引
      }
    },
    hasQueryParamFlag(newVal, oldVal) {
      this.init()
    },
    ordtaskidToWatch(newVal, oldVal) {
      console.log("ordtaskidToWatch, newVal, oldVal: ", newVal, oldVal)
      if (newVal === null) {
        return;
      }
      // 编辑参数进入会监听，不发起请求
      if (this.editInitFlag) {
        this.editInitFlag = false;
        return;
      }

      // 测试ordtaskId：1478626674007040
      if (this.reportType === 'HlTemp') {
        getTemGradientByOrdtaskId(newVal, {conditionName: "测试内容"}).then(res => {
          if (res.success) {
            const temGradientList = res.data
            for (let i = 0; i < temGradientList.length; i++) {
              const temGradient = temGradientList[i]
              if (this.hlTempOrCRateParam.stepParamList.length > i) {
                this.hlTempOrCRateParam.stepParamList[i].tempOrCRate = temGradient.temperature
              } else {
                this.hlTempOrCRateParam.stepParamList.push({_uid: this.generateUID(), tempOrCRate: temGradient.temperature})
              }
            }
          }

          this.$emit('handleVerify', this._handleVerify())
        })
      } else if (this.reportType === 'CRate' && this.chOrDchTypeName) {
        this.handleCRateListChange(newVal)
      }
    },
  },
  created() {
    this.init()
  },
  mounted() {
    this.handleHeight()

    this.$nextTick(() => {
      let tableContainer = this.$refs.tableContainer
      this.rowDrop(tableContainer)

      let paramTableContainer = this.$refs.paramTableContainer
      this.rowDropOfStepParam(paramTableContainer)
    })
  },
  methods: {
    generateUID() {
      return 'uid_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    },
    handleChOrDchTypeChange(e) {
      this.chOrDchTypeName = e.target.value === 'Charge' ? '充电' : e.target.value === 'Discharge' ? '放电' : ''
      if (this.reportType === 'CRate' && this.hlTempOrCRateParam.orderDataList.length > 0) {
        this.handleCRateListChange(this.hlTempOrCRateParam.orderDataList[0].ordtaskid)
      }
      this.$emit('handleVerify', this._handleVerify())
    },
    handleCRateListChange(ordtaskid) {
      getConditionsByOrdtaskId({ordtaskid: ordtaskid, conditionname: `${this.chOrDchTypeName}倍率`}).then(res => {
        if (res.success && Array.isArray(res.data) && res.data.length > 0) {
          const conditionvalue = res.data[0].conditionvalue
          const rateList = (conditionvalue + '').split(',').map(item => item.replace('C', ''))
          for (let i = 0; i < rateList.length; i++) {
            if (this.hlTempOrCRateParam.stepParamList.length > i) {
              this.hlTempOrCRateParam.stepParamList[i].tempOrCRate = rateList[i]
            } else {
              this.hlTempOrCRateParam.stepParamList.push({_uid: this.generateUID(), tempOrCRate: rateList[i]})
            }
          }
          this.$emit('handleVerify', this._handleVerify())
        }
      })
    },
    handleHeight() {
      const minHeight = document.body.clientHeight - 42 - this.width - 2*8 - 8 - 37 - 2*8
      document.documentElement.style.setProperty(`--height`, `${minHeight}px`)
    },
    rowDrop(dom) {
      new Sortable.create(dom.querySelector('.ant-table>.ant-table-content .ant-table-tbody'), {
        handle: '.drag', // 按钮拖拽
        animation: 150,
        onEnd: ({newIndex, oldIndex}) => {
          // 拖拽后回调
          const currRow = this.hlTempOrCRateParam.orderDataList.splice(oldIndex, 1)[0]
          this.hlTempOrCRateParam.orderDataList.splice(newIndex, 0, currRow)
        }
      })
    },
    rowDropOfStepParam(dom) {
      new Sortable.create(dom.querySelector('.ant-table>.ant-table-content .ant-table-tbody'), {
        handle: '.drag', // 按钮拖拽
        animation: 150,
        onEnd: ({newIndex, oldIndex}) => {
          // 拖拽后回调
          const currRow = this.hlTempOrCRateParam.stepParamList.splice(oldIndex, 1)[0]
          this.hlTempOrCRateParam.stepParamList.splice(newIndex, 0, currRow)
        }
      })
    },
    init() {
      if (this.hasQueryParamFlag && 'tempOrCRate' in this.reportQueryParam) {
        this.editInitFlag = true
        this.hlTempOrCRateParam = this.reportQueryParam
        this.chOrDchTypeName = this.hlTempOrCRateParam.chOrDchType === 'Charge' ? '充电' : this.hlTempOrCRateParam.chOrDchType === 'Discharge' ? '放电' : ''
        this.hlTempOrCRateParam.stepParamList.forEach((item) => {
          item._uid = item._uid || this.generateUID()
        })
        this.$emit('handleVerify', this._handleVerify())
      }
    },
    deleteOrderDataOne(record, index) {
      this.hlTempOrCRateParam.orderDataList.splice(index, 1)
      const findIndex = this.deleteSelectedRowKeys.findIndex(item => item == record.uuid)
      if (findIndex !== -1) {
        this.deleteSelectedRowKeys.splice(findIndex, 1)
      }
      this.$emit('handleVerify', this._handleVerify())
    },
    deleteSelectOrderData() {
      this.hlTempOrCRateParam.orderDataList = this.hlTempOrCRateParam.orderDataList.filter(item => !this.deleteSelectedRowKeys.includes(item.uuid));
      this.deleteSelectedRowKeys = []
      this.$emit('handleVerify', this._handleVerify())
    },
    deleteSelectStepParam() {
      this.hlTempOrCRateParam.stepParamList = this.hlTempOrCRateParam.stepParamList.filter(item => !this.stepParamSelectedRowKeys.includes(item._uid));
      this.stepParamSelectedRowKeys = []
      this.hlTempOrCRateParam.standardIndex = null
      this.$emit('handleVerify', this._handleVerify())
    },
    addOne(index) {
      this.hlTempOrCRateParam.stepParamList.splice(index, 0, {_uid: this.generateUID()})
      this.$emit('handleVerify', this._handleVerify())
    },
    deleteOne(index) {
      this.hlTempOrCRateParam.stepParamList.splice(index, 1)
      if (this.hlTempOrCRateParam.standardIndex >= index) {
        this.hlTempOrCRateParam.standardIndex = null
      }
      this.$emit('handleVerify', this._handleVerify())
    },
    onRadioChange(index) {
      // 选择当前行索引为基准工步索引
      this.hlTempOrCRateParam.standardIndex = index

      this.$emit('handleVerify', this._handleVerify())
    },
    handleNumberBlur(targetList = null, index = null, target = null) {
      /*
      if (targetList && typeof index == 'number' && target && this.hlTempOrCRateParam[targetList][index][target]) {
        this.hlTempOrCRateParam[targetList][index][target] = Number.parseFloat((this.hlTempOrCRateParam[targetList][index][target]+'').replaceAll(/[^0-9.-]/g, '').replaceAll(/-+/g, '-').replaceAll(/\.+/g, '.').replaceAll(/^\.|\.$/g, ''))
      }
       */

      if (target && this.hlTempOrCRateParam[target]) {
        this.hlTempOrCRateParam[target] = Number.parseFloat((this.hlTempOrCRateParam[target]+'').replaceAll(/[^0-9.-]/g, '').replaceAll(/-+/g, '-').replaceAll(/\.+/g, '.').replaceAll(/^\.|\.$/g, ''))
      }

      this.$emit('handleVerify', this._handleVerify())
    },
    verifyStepNumber(index, type) {
      if (this.hlTempOrCRateParam.stepParamList[index][type]) {
        this.hlTempOrCRateParam.stepParamList[index][type] = (this.hlTempOrCRateParam.stepParamList[index][type] + "").replaceAll(type === "ceStep" && this.reportType === 'HlTemp' ? /[^0-9,，]/g : /[^0-9]/g, "")
      }

      this.$emit('handleVerify', this._handleVerify())
    },
    copyFromExcel(event, startColumnIndex, rowIndex) {
      // excel复制末尾会有换行符，split后数组多一个空串，先去除
      let rows = event.clipboardData.getData("text").replace(/[\n]$/, "").split("\n")

      // 填充参数列表
      if (this.hlTempOrCRateParam.stepParamList.length < rowIndex + rows.length) {
        this.hlTempOrCRateParam.stepParamList.push(...Array.from({ length: rowIndex + rows.length - this.hlTempOrCRateParam.stepParamList.length }, () => ({_uid: this.generateUID()})))
      }

      let firstData
      // 起始行：rowIndex，结束行：math.min(rowIndex + rows.length, this.hlTempOrCRateParam.stepParamList.length)
      for (let i = rowIndex; i < this.hlTempOrCRateParam.stepParamList.length && i < rowIndex + rows.length; i++) {
        let rowList = rows[i-rowIndex].split("\t")
        if (i === rowIndex) {
          let type = this.stepParamColumns[startColumnIndex].dataIndex
          firstData = rowList[0].replaceAll(type === "tempOrCRate" ? /[^0-9.-/]/g : type === "ceStep" && this.reportType === 'HlTemp' ? /[^0-9,，]/g : /[^0-9]/g, "")
        }

        // 起始列：startColumnIndex所在列，结束列：math.min(startColumnIndex + rowList.length, (this.reportType === 'HlTemp' ? 3 : 2))
        for (let j = startColumnIndex; j <= (this.reportType === 'HlTemp' ? 3 : 2) && j < startColumnIndex + rowList.length; j++) {
          const type = this.stepParamColumns[j].dataIndex
          this.hlTempOrCRateParam.stepParamList[i][type] = rowList[j-startColumnIndex].replaceAll(type === "tempOrCRate" ? /[^0-9.-/]/g : type === "ceStep" && this.reportType === 'HlTemp' ? /[^0-9,，]/g : /[^0-9]/g, "")
        }
      }

      // 解决第一个单元格被覆盖的问题
      setTimeout(() => {
        const type = this.stepParamColumns[startColumnIndex].dataIndex
        this.hlTempOrCRateParam.stepParamList[rowIndex][type] = firstData

        this.$emit('handleVerify', this._handleVerify())
      }, 10)
    },
    // 校验
    _handleVerify() {
      this.$forceUpdate()

      // ---------------------- 校验：测试数据选择 ------------------------
      let orderDataList = this.hlTempOrCRateParam.orderDataList
      if (!Array.isArray(orderDataList) || orderDataList.length === 0) {
        return [false, '请选择测试数据']
      }

      // ---------------------- 校验：信息填写 ------------------------
      if (!this.hlTempOrCRateParam.projectName) {
        return [false, '请填写项目名称']
      }
      if (!this.hlTempOrCRateParam.phase) {
        return [false, '请填写样品阶段']
      }
      if (!this.hlTempOrCRateParam.tempOrCRate && this.hlTempOrCRateParam.tempOrCRate !== 0) {
        return [false, '请填写' + (this.reportType === 'HlTemp' ? '倍率' : '温度')]
      }
      if (!this.hlTempOrCRateParam.yaxisMax && this.hlTempOrCRateParam.yaxisMax !== 0) {
        return [false, '请填写图表电压上限']
      }
      if (!this.hlTempOrCRateParam.yaxisMin && this.hlTempOrCRateParam.yaxisMin !== 0) {
        return [false, '请填写图表电压下限']
      }

      if (!this.hlTempOrCRateParam.chOrDchType) {
        return [false, '请选择充放电类型']
      }

      // ---------------------- 校验：工步参数填写 ------------------------
      let stepParamList = this.hlTempOrCRateParam.stepParamList
      if (!Array.isArray(stepParamList) || stepParamList.length === 0) {
        return [false, '请填写模型生成逻辑']
      }
      for (let i = 0; i < stepParamList.length; i++) {
        const stepParam = stepParamList[i];
        if (!stepParam.tempOrCRate && stepParam.tempOrCRate !== 0) {
          return [false, '请填写模型生成逻辑第' + (i+1) + '行' + (this.reportType === 'HlTemp' ? '温度' : '倍率')]
        }
        if (!stepParam.ceStep) {
          return [false, '请填写模型生成逻辑第' + (i+1) + '行' + this.chOrDchTypeName + '工步号']
        }
      }

      // ---------------------- 校验：基准工步索引，基准工步行数据 ------------------------
      let standardIndex = this.hlTempOrCRateParam.standardIndex
      if (typeof standardIndex !== 'number') {
        return [false, '请选择基准工步']
      }

      this.handleCeStep()

      return [true, this.hlTempOrCRateParam]
      },
    handleCeStep() {
      let stepParamList = this.hlTempOrCRateParam.stepParamList
      if (Array.isArray(stepParamList) && stepParamList.length > 0) {
        for (let i = 0; i < stepParamList.length; i++) {
          if (stepParamList[i].ceStep) {
            stepParamList[i].ceStep = (stepParamList[i].ceStep + "")
                .replaceAll(/[\t\n\s]/g, ',').replaceAll(/[^0-9,]/g, ',') // 替换所有非法字符为英文逗号
                .replaceAll(/,+/g, ",") // 将连续的逗号替换为单个逗号
                // .replaceAll(/^,|,$/g, "") // 去除字符串开头和结尾的逗号

            stepParamList[i].ceStepList = stepParamList[i].ceStep.replaceAll(/^,|,$/g, "").split(",")
          } else {
            stepParamList[i].ceStepList = []
          }
        }
      }
    },
  }
}
</script>

<style lang="less" scoped>
:root {
  --height: calc(100vh - 42px - 8*2px - 8px - 37px - 2*8px);
}

// 通用
.mr5 {
  margin-right: 5px;
}

.mt8 {
  margin-top: 8px;
}

.mt10 {
  margin-top: 10px;
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

h3 {
  font-size: 15px;
  font-weight: bold;
  padding: 0;
  margin: 0;
}

.flex-sb-center-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hl-temp-wrapper {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  width: calc(100vw - 40px - 12*2px);
}

.left-content {
  min-height: var(--height);
  width: 50%;
}

/deep/ .left-content .ant-table-body {
  height: calc(var(--height) - 2*10px - 32px - 10px - 26px - 35px + 30px);
  overflow: auto;
}

.right-content {
  width: 50%;
}

/deep/ .right-content .ant-table-body {
  height: calc(var(--height) - 130.5px - 8px - 2*10px - 22.5px - 8px - 32px - 10px - 35px + 4px);
  overflow: auto;
}

.block {
  height: fit-content;
  padding: 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(51, 51, 51, 0.1);
  position: relative;
}

/deep/ .ant-table-thead > tr > th {
  padding: 5px !important;
  font-size: 13px !important;
}

/deep/ .ant-table-tbody > tr > td {
  padding: 0px !important;
  height: 24px !important;
  font-size: 12px !important;
}

/deep/ .ant-table-placeholder {
  border: none !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0;
}

/deep/ .ant-empty-normal {
  margin: -2px 0;
}

/deep/ .ant-empty-image {
  display: none;
}

/deep/ .ant-table-row-expand-icon {
  margin-right: 0px;
}

/deep/ .ant-table-footer {
  padding: 0;
}

/* /deep/ .right-content .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}

/deep/ .left-content .ant-table-body::-webkit-scrollbar {
  height: 8px;
  width: 6px;
}

/deep/ .right-content .ant-table-body::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;

  background: #dddbdb;
} */

/* /deep/ .right-content .ant-table-body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0;
  background: #f1f1f1;
} */

.footer-btn {
  width: 100%;
  height: 32px;
  border: 1px solid #e8e8e8;
  background: #fff;
  color: #999;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.footer-btn:hover {
  color: #1890ff;
}

.btn-icon {
  font-size: 16px;
  color: #1890ff;
  cursor: pointer;
}

/* /deep/.left-content .ant-table-header colgroup col:last-child {

  min-width: 53.3px !important;
} */
/deep/ .ant-table-body {
  border: 1px solid #e8e8e8;
}

/deep/ #orderDataTable .ant-table-thead > tr > th {
  padding: 2px 0 !important;
  font-size: 12px !important;
}
</style>