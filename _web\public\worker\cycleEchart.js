// ✅ 使用标准的 addEventListener 方式监听消息
self.addEventListener('message', function(e) {
  const { type,payload } = e.data;

  if (type === 'PROCESS_DATA') {
      const options = JSON.parse(payload)

      const { sampleCodeList,needEtp,hasChCeStep,hasDcirGroup,hasRptChCeStep,cycCapTableList,rptTableList,dchCeStepTiTleList,dcirTiTleList,queryParam,cycCapColumns,cycEngColumns,rptCapColumns,rptEngColumns } = options;

      if(cycCapTableList && cycCapTableList.length > 0){
        let primaryObjectMapOne = cycCapTableList[0].primaryObjectMap

        // 安全检查
        if (!primaryObjectMapOne) {
          console.error('primaryObjectMapOne 为空');
          return;
        }

        if (Array.isArray(sampleCodeList) && sampleCodeList.length > 0) {
              cycCapColumns[1].children = []
              cycCapColumns[2].children = []
              cycEngColumns[1].children = []
              cycEngColumns[2].children = []

              if (hasChCeStep) {
                cycCapColumns.push({headerName:'充电容量/Ah',children: []})
                cycEngColumns.push({headerName:'充电能量/Wh',children: []})
              }

              if (needEtp) {
                cycCapColumns.push({headerName:'ETP/Wh',children: []})
                cycEngColumns.push({headerName:'充电能量/Wh',children: []})
              }

              for (let i = 0; i < sampleCodeList.length; i++) {
                let sampleCode = sampleCodeList[i]

                // 安全检查：确保 sampleCode 对应的数据存在
                if (!primaryObjectMapOne[sampleCode]) {
                  console.warn(`sampleCode ${sampleCode} 在 primaryObjectMapOne 中不存在`);
                  continue;
                }

                let batteryCode = primaryObjectMapOne[sampleCode].batteryCode || sampleCode

                cycCapColumns[1].children.push(getChildrenColumns(sampleCode, batteryCode, 'capacity'))
                cycCapColumns[2].children.push(getChildrenColumns(sampleCode, batteryCode, 'capRetRate'))
                cycEngColumns[1].children.push(getChildrenColumns(sampleCode, batteryCode, 'energy'))
                cycEngColumns[2].children.push(getChildrenColumns(sampleCode, batteryCode, 'engRetRate'))

                

                if (hasChCeStep) {
                cycCapColumns[3].children.push(getChildrenColumns(sampleCode, batteryCode, 'chCapacity'))
                cycEngColumns[3].children.push(getChildrenColumns(sampleCode, batteryCode, 'chEnergy'))
                }

                const colIndex1 = cycCapColumns.findIndex(item => item.headerName === 'ETP/Wh')
                if (needEtp && colIndex1 !== -1) {
                  cycCapColumns[colIndex1].children.push(
                    getChildrenColumns(sampleCode, batteryCode, 'etp')
                  )
                  cycEngColumns[colIndex1].children.push(
                    getChildrenColumns(sampleCode, batteryCode, 'etp')
                  )
                }
              }
            }
      }

      if (queryParam.ceStepTiTle) {
        rptCapColumns[1].headerName =queryParam.ceStepTiTle + '_' + '放电容量/Ah'
        rptEngColumns[1].headerName = queryParam.ceStepTiTle + '_' + '放电能量/Wh'
      }

      if (rptTableList && rptTableList.length > 0) {
        let primaryObjectMapOne = rptTableList[0].primaryObjectMap

        // 安全检查
        if (!primaryObjectMapOne) {
          console.error('rptTableList primaryObjectMapOne 为空');
          return;
        }

        if (Array.isArray(sampleCodeList) && sampleCodeList.length > 0) {
          rptCapColumns[1].children = []
          rptCapColumns[2].children = []

          for (let i = 0; i < dchCeStepTiTleList.length; i++) {
            rptCapColumns.push({headerName: dchCeStepTiTleList[i] + "_放电容量/Ah",children: []})
          }
          if (hasDcirGroup) {
            for (let i = 0; i < dcirTiTleList.length; i++) {
              rptCapColumns.push({headerName: dcirTiTleList[i] + "_DCIR/mΩ",children: []},{headerName: dcirTiTleList[i] + "_DCIR增长率/%",children: []})
            }
          }

          rptEngColumns[1].children = []
          rptEngColumns[2].children = []

          for (let i = 0; i < dchCeStepTiTleList.length; i++) {
            rptEngColumns.push({headerName: dchCeStepTiTleList[i] + "_放电能量/Wh",children: []})
          }
          if (hasDcirGroup) {
            for (let i = 0; i < dcirTiTleList.length; i++) {
              rptEngColumns.push({headerName: dcirTiTleList[i] + "_DCIR/mΩ",children: []},{headerName: dcirTiTleList[i] + "_DCIR增长率/%",children: []})
            }
          }

          if (hasRptChCeStep) {
            rptCapColumns.push({headerName:'充电容量/Ah',children: []},{headerName:'充电恒流比',children: []})
            rptEngColumns.push({headerName:'充电能量/Wh',children: []}) 
          }

          if (needEtp) {
            rptCapColumns.push({headerName:'ETP/Wh',children: []})
            rptEngColumns.push({headerName:'ETP/Wh',children: []})
          }

          for (let i = 0; i < sampleCodeList.length; i++) {
            let sampleCode = sampleCodeList[i]

            // 安全检查：确保 sampleCode 对应的数据存在
            if (!primaryObjectMapOne[sampleCode]) {
              console.warn(`rptTableList: sampleCode ${sampleCode} 在 primaryObjectMapOne 中不存在`);
              continue;
            }

            let batteryCode = primaryObjectMapOne[sampleCode].batteryCode || sampleCode

            rptCapColumns[1].children.push(getChildrenColumns(sampleCode, batteryCode, 'capacity'))
            rptCapColumns[2].children.push(getChildrenColumns(sampleCode, batteryCode, 'capRetRate'))

            for (let j = 0; j < dchCeStepTiTleList.length; j++) {
              rptCapColumns[3 + j].children.push(getChildrenColumns(sampleCode, batteryCode, 'dchCapacity'+j))
            }
            if (hasDcirGroup) {
              for (let j = 0; j < dcirTiTleList.length; j++) {
                rptCapColumns[dchCeStepTiTleList.length + 3 + 2 * j].children.push(getChildrenColumns(sampleCode, batteryCode, 'dcir'+j))
                rptCapColumns[dchCeStepTiTleList.length + 4 + 2 * j].children.push(getChildrenColumns(sampleCode, batteryCode, 'dcirIncRate'+j))
              }
            }

            rptEngColumns[1].children.push(getChildrenColumns(sampleCode, batteryCode, 'energy'))
            rptEngColumns[2].children.push(getChildrenColumns(sampleCode, batteryCode, 'engRetRate'))
            for (let j = 0; j < dchCeStepTiTleList.length; j++) {
              rptEngColumns[3 + j].children.push(getChildrenColumns(sampleCode, batteryCode, 'dchEnergy'+j))
            }
            if (hasDcirGroup) {
              for (let j = 0; j < dcirTiTleList.length; j++) {
                rptEngColumns[dchCeStepTiTleList.length + 3 + 2*j].children.push(getChildrenColumns(sampleCode, batteryCode, 'dcir'+j))
                rptEngColumns[dchCeStepTiTleList.length + 4 + 2*j].children.push(getChildrenColumns(sampleCode, batteryCode, 'dcirIncRate'+j))
              }
            }

            if (hasRptChCeStep) {
              rptCapColumns[3 + dchCeStepTiTleList.length + 2 * dcirTiTleList.length].children.push(getChildrenColumns(sampleCode, batteryCode, 'chCapacity'))
              rptCapColumns[4 + dchCeStepTiTleList.length + 2 * dcirTiTleList.length].children.push(getChildrenColumns(sampleCode, batteryCode, 'chCccapacityRate'))
              rptEngColumns[3 + dchCeStepTiTleList.length + 2 * dcirTiTleList.length].children.push(getChildrenColumns(sampleCode, batteryCode, 'chEnergy'))
            }
            if (needEtp) {
              const index1 = 3 + dchCeStepTiTleList.length + 2 * dcirTiTleList.length + (hasRptChCeStep ? 2 : 0)
              const index2 = 3 + dchCeStepTiTleList.length + 2 * dcirTiTleList.length + (hasRptChCeStep ? 1 : 0)

              rptCapColumns[index1].children.push(getChildrenColumns(sampleCode, batteryCode, 'etp'))
              rptEngColumns[index2].children.push(getChildrenColumns(sampleCode, batteryCode, 'etp'))
            }
          }
        }
      }

      function getChildrenColumns(sampleCode, batteryCode, columnKey){
        let result = {
            headerName: batteryCode,
            field: `primaryObjectMap[${sampleCode}][${columnKey}]`,
            sampleCode,
            columnKey,
            isHaveCell:true,
            // cellRenderer: params  => {
            //   return params.data.primaryObjectMap[sampleCode][columnKey]
            // }
          }

          if (sampleCode !== batteryCode) {
            result = {
              headerName: sampleCode,
              width: "100px",
              children: [
                result
              ]
            }
          }

          return result
      }

      const payloadReturn = {
        cycCapColumns,
        cycEngColumns,
        rptCapColumns,
        rptEngColumns
      }

      // 返回结果
      self.postMessage({
        type: 'DATA_PROCESSED',
        payload: JSON.stringify(payloadReturn) 
      });
  }
}, false);

// 发送初始化完成消息
self.postMessage({
  type: 'WORKER_READY',
  message: 'Cycle chart worker initialized successfully',
  success: true
});


